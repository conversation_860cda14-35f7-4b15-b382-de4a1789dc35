<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <!-- 主标语：居中显示，类似 Momentum 的简洁风格 -->
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="What you are seeking is also seeking you" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="12" translatesAutoresizingMaskIntoConstraints="NO" id="mAB-vj-IT9">
                    <fontDescription key="fontDescription" name="Avenir-Light" family="Avenir" pointSize="20"/>
                    <color key="textColor" red="0.2" green="0.2" blue="0.2" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>

                <!-- 副标语：Stay chill，稍小一些 -->
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Stay chill" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="9" translatesAutoresizingMaskIntoConstraints="NO" id="HEx-7v-eGl">
                    <fontDescription key="fontDescription" name="Avenir-Medium" family="Avenir" pointSize="18"/>
                    <color key="textColor" red="0.3" green="0.3" blue="0.3" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>

                <!-- StarFlowLab 品牌信息：底部显示，更小更轻 -->
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="StarFlowLab ™️ Seek / Communication / Automation / Collaboration" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="8" translatesAutoresizingMaskIntoConstraints="NO" id="second-label">
                    <fontDescription key="fontDescription" name="Avenir-Book" family="Avenir" pointSize="12"/>
                    <color key="textColor" red="0.6" green="0.6" blue="0.6" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" red="0.98" green="0.98" blue="0.98" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <!-- 主标语居中显示 -->
                <constraint firstItem="mAB-vj-IT9" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="main-title-centerX"/>
                <constraint firstItem="mAB-vj-IT9" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" constant="-40" id="main-title-centerY"/>
                <constraint firstItem="mAB-vj-IT9" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="iN0-l3-epB" secondAttribute="leading" constant="30" id="main-title-leading"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="mAB-vj-IT9" secondAttribute="trailing" constant="30" id="main-title-trailing"/>

                <!-- Stay chill 在主标语下方 -->
                <constraint firstItem="HEx-7v-eGl" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="subtitle-centerX"/>
                <constraint firstItem="HEx-7v-eGl" firstAttribute="top" secondItem="mAB-vj-IT9" secondAttribute="bottom" constant="20" id="subtitle-top"/>
                <constraint firstItem="HEx-7v-eGl" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="iN0-l3-epB" secondAttribute="leading" constant="30" id="subtitle-leading"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="HEx-7v-eGl" secondAttribute="trailing" constant="30" id="subtitle-trailing"/>

                <!-- StarFlowLab 品牌信息在底部 -->
                <constraint firstItem="second-label" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="brand-centerX"/>
                <constraint firstAttribute="bottom" secondItem="second-label" secondAttribute="bottom" constant="60" id="brand-bottom"/>
                <constraint firstItem="second-label" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="40" id="brand-leading"/>
                <constraint firstAttribute="trailing" secondItem="second-label" secondAttribute="trailing" constant="40" id="brand-trailing"/>
            </constraints>
            <nil key="simulatedStatusBarMetrics"/>
            <point key="canvasLocation" x="548" y="454.72263868065971"/>
        </view>
    </objects>
</document>
