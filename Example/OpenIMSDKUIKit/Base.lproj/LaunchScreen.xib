<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="StarFlowLab ™️ Seek / Communication / Automation / Collaboration" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="9" translatesAutoresizingMaskIntoConstraints="NO" id="second-label">
                    <rect key="frame" x="20" y="587" width="335" height="20"/>
                    <fontDescription key="fontDescription" name="Avenir-Book" family="Avenir" pointSize="14"/>
                    <color key="textColor" red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Copyright © 2024" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="9" translatesAutoresizingMaskIntoConstraints="NO" id="copyright-label">
                    <rect key="frame" x="20" y="620.5" width="335" height="16.5"/>
                    <fontDescription key="fontDescription" name="Avenir-Book" family="Avenir" pointSize="12"/>
                    <color key="textColor" red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="What you are seeking is also seeking you在找的也正在找你" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="9" translatesAutoresizingMaskIntoConstraints="NO" id="mAB-vj-IT9">
                    <rect key="frame" x="24" y="275" width="335" height="22"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" name="Avenir-Book" family="Avenir" pointSize="16"/>
                    <color key="textColor" red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="Stay chill" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="9" translatesAutoresizingMaskIntoConstraints="NO" id="HEx-7v-eGl">
                    <rect key="frame" x="24" y="314" width="335" height="22"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" name="Avenir-Book" family="Avenir" pointSize="16"/>
                    <color key="textColor" red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="copyright-label" secondAttribute="bottom" constant="30" id="copyright-bottom"/>
                <constraint firstItem="copyright-label" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="20" id="copyright-leading"/>
                <constraint firstAttribute="trailing" secondItem="copyright-label" secondAttribute="trailing" constant="20" id="copyright-trailing"/>
                <constraint firstAttribute="bottom" secondItem="second-label" secondAttribute="bottom" constant="60" id="second-bottom"/>
                <constraint firstItem="second-label" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="20" id="second-leading"/>
                <constraint firstAttribute="trailing" secondItem="second-label" secondAttribute="trailing" constant="20" id="second-trailing"/>
            </constraints>
            <nil key="simulatedStatusBarMetrics"/>
            <point key="canvasLocation" x="548" y="454.72263868065971"/>
        </view>
    </objects>
</document>
