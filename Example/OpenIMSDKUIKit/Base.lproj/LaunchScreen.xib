<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <!-- 黑点装饰 -->
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dot-decoration">
                    <color key="backgroundColor" red="0.1" green="0.1" blue="0.1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <real key="value">3</real>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>

                <!-- 主标语：Stay chill，使用粗体，更大字号 -->
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Stay chill" textAlignment="left" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="12" translatesAutoresizingMaskIntoConstraints="NO" id="HEx-7v-eGl">
                    <fontDescription key="fontDescription" name="Avenir-Heavy" family="Avenir" pointSize="24"/>
                    <color key="textColor" red="0.15" green="0.15" blue="0.15" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>

                <!-- 副标语：What you are seeking is also seeking you，更小更灰 -->
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="What you are seeking is also seeking you" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="mAB-vj-IT9">
                    <fontDescription key="fontDescription" name="Avenir-Book" family="Avenir" pointSize="16"/>
                    <color key="textColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>

                <!-- StarFlowLab 品牌信息：底部显示，更小更轻 -->
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="StarFlowLab ™️ Seek / Communication / Automation / Collaboration" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="8" translatesAutoresizingMaskIntoConstraints="NO" id="second-label">
                    <fontDescription key="fontDescription" name="Avenir-Book" family="Avenir" pointSize="12"/>
                    <color key="textColor" red="0.6" green="0.6" blue="0.6" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" red="0.98" green="0.98" blue="0.98" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <!-- 黑点装饰：圆形，6x6 大小 -->
                <constraint firstItem="dot-decoration" firstAttribute="width" constant="6" id="dot-width"/>
                <constraint firstItem="dot-decoration" firstAttribute="height" constant="6" id="dot-height"/>
                <constraint firstItem="dot-decoration" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" constant="-40" id="dot-centerY"/>

                <!-- Stay chill：在黑点右侧，居中对齐 -->
                <constraint firstItem="HEx-7v-eGl" firstAttribute="leading" secondItem="dot-decoration" secondAttribute="trailing" constant="12" id="title-leading"/>
                <constraint firstItem="HEx-7v-eGl" firstAttribute="centerY" secondItem="dot-decoration" secondAttribute="centerY" id="title-centerY"/>

                <!-- 黑点和Stay chill整体居中 -->
                <constraint firstItem="dot-decoration" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="iN0-l3-epB" secondAttribute="leading" constant="30" id="dot-leading-min"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="HEx-7v-eGl" secondAttribute="trailing" constant="30" id="title-trailing-min"/>

                <!-- 计算整体居中：黑点左边距 + 黑点宽度 + 间距 + 文字宽度的一半 = 屏幕中心 -->
                <constraint firstItem="iN0-l3-epB" firstAttribute="centerX" secondItem="dot-decoration" secondAttribute="trailing" constant="9" id="group-centerX"/>

                <!-- 副标语：What you are seeking is also seeking you，在Stay chill下方 -->
                <constraint firstItem="mAB-vj-IT9" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="subtitle-centerX"/>
                <constraint firstItem="mAB-vj-IT9" firstAttribute="top" secondItem="HEx-7v-eGl" secondAttribute="bottom" constant="16" id="subtitle-top"/>
                <constraint firstItem="mAB-vj-IT9" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="iN0-l3-epB" secondAttribute="leading" constant="30" id="subtitle-leading"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="mAB-vj-IT9" secondAttribute="trailing" constant="30" id="subtitle-trailing"/>

                <!-- StarFlowLab 品牌信息在底部 -->
                <constraint firstItem="second-label" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="brand-centerX"/>
                <constraint firstAttribute="bottom" secondItem="second-label" secondAttribute="bottom" constant="60" id="brand-bottom"/>
                <constraint firstItem="second-label" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="40" id="brand-leading"/>
                <constraint firstAttribute="trailing" secondItem="second-label" secondAttribute="trailing" constant="40" id="brand-trailing"/>
            </constraints>
            <nil key="simulatedStatusBarMetrics"/>
            <point key="canvasLocation" x="548" y="454.72263868065971"/>
        </view>
    </objects>
</document>
