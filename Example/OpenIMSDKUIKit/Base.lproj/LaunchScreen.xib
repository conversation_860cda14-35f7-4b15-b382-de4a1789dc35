<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <!-- 主标语：● Stay Chill，黑点更大，字体不加粗 -->
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="⬤ Stay Chill" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="12" translatesAutoresizingMaskIntoConstraints="NO" id="HEx-7v-eGl">
                    <fontDescription key="fontDescription" name="Avenir-Book" family="Avenir" pointSize="18"/>
                    <color key="textColor" red="0.2" green="0.2" blue="0.2" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <!-- 副标语：更灰色、更淡 -->
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="What you are seeking is also seeking you" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="mAB-vj-IT9">
                    <fontDescription key="fontDescription" name="Avenir-Book" family="Avenir" pointSize="16"/>
                    <color key="textColor" red="0.7" green="0.7" blue="0.7" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <!-- 功能描述：第一行，更淡的灰色 -->
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Seek / Communication / Automation / Collaboration" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="8" translatesAutoresizingMaskIntoConstraints="NO" id="second-label">
                    <fontDescription key="fontDescription" name="Avenir-Book" family="Avenir" pointSize="12"/>
                    <color key="textColor" red="0.75" green="0.75" blue="0.75" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>

                <!-- 品牌名称：第二行，更淡的灰色 -->
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="StarFlowLab ™️" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="8" translatesAutoresizingMaskIntoConstraints="NO" id="brand-label">
                    <fontDescription key="fontDescription" name="Avenir-Book" family="Avenir" pointSize="12"/>
                    <color key="textColor" red="0.75" green="0.75" blue="0.75" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" red="0.97999999999999998" green="0.97999999999999998" blue="0.97999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <!-- ● Stay chill：居中显示 -->
                <constraint firstItem="HEx-7v-eGl" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="title-centerX"/>
                <constraint firstItem="HEx-7v-eGl" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" constant="-40" id="title-centerY"/>
                <constraint firstItem="HEx-7v-eGl" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="iN0-l3-epB" secondAttribute="leading" constant="30" id="title-leading"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="HEx-7v-eGl" secondAttribute="trailing" constant="30" id="title-trailing"/>

                <!-- 副标语：What you are seeking is also seeking you，在Stay chill下方 -->
                <constraint firstItem="mAB-vj-IT9" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="subtitle-centerX"/>
                <constraint firstItem="mAB-vj-IT9" firstAttribute="top" secondItem="HEx-7v-eGl" secondAttribute="bottom" constant="16" id="subtitle-top"/>
                <constraint firstItem="mAB-vj-IT9" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="iN0-l3-epB" secondAttribute="leading" constant="30" id="subtitle-leading"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="mAB-vj-IT9" secondAttribute="trailing" constant="30" id="subtitle-trailing"/>

                <!-- 功能描述在底部上方 -->
                <constraint firstItem="second-label" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="features-centerX"/>
                <constraint firstAttribute="bottom" secondItem="second-label" secondAttribute="bottom" constant="80" id="features-bottom"/>
                <constraint firstItem="second-label" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="40" id="features-leading"/>
                <constraint firstAttribute="trailing" secondItem="second-label" secondAttribute="trailing" constant="40" id="features-trailing"/>

                <!-- 品牌名称在功能描述下方 -->
                <constraint firstItem="brand-label" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="brand-centerX"/>
                <constraint firstItem="brand-label" firstAttribute="top" secondItem="second-label" secondAttribute="bottom" constant="8" id="brand-top"/>
                <constraint firstItem="brand-label" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="iN0-l3-epB" secondAttribute="leading" constant="40" id="brand-leading"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="brand-label" secondAttribute="trailing" constant="40" id="brand-trailing"/>
            </constraints>
            <nil key="simulatedStatusBarMetrics"/>
            <point key="canvasLocation" x="548" y="454.72263868065971"/>
        </view>
    </objects>
</document>
